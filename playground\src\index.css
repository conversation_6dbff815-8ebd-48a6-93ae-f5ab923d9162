:root {
  --primary-color: #4a79d8;
  --body-background-color: #fdfdfd;

  --content-font-size-large: 24px;
  --content-font-size-middle: 16px;
  --content-font-size-small: 12px;

  --content-font-size: var(--content-font-size-middle);
}


body {
  margin: 0 !important;
  padding: 0 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--body-background-color);
}

/* TableForm 组件样式 */
.table-form-container {
  width: 100%;
  box-sizing: border-box;
}

.table-form-page-header {
  /* margin-bottom: 2px; */
  padding: 8px 16px 0;
}

.table-form-page-title {
  text-align: center;
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: bold;
  color: #262626;
}

.table-form-page-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  color: #262626;
  /* border-bottom: 1px solid #ccc; */
}

.table-form-page-info-left {
  flex: 1;
  text-align: left;
}

.table-form-page-info-right {
  flex: 1;
  text-align: right;
}

.table-form-wrapper {
  margin: 16px;
  margin-top:0;
  /* border: 1px solid #e8e8e8; */
  /* border-radius: 6px; */
  overflow: hidden;
  box-sizing: border-box;
}

.table-form-header {
  /* background-color: #1890ff;
  color: white; */
  padding: 10px 0px;
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  /* font-weight: bold; */
  font-size: 18px;
}

.table-form-header-clickable {
  cursor: pointer;
}

.table-form-header-arrow {
  border-left:8px solid #4a79d8;
  margin-right:6px;
  display:inline-block;
  /* width:4px; */
  height:24px
  /* transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-block; */
}

.table-form-header-arrow-collapsed {
  transform: rotate(180deg);
}

.table-form-content {
  width: 100%;
  margin-bottom: 10px;
  border: 1px solid #d7d7d7;
  overflow: hidden;
  flex-direction: column;
  display: flex;
  box-sizing: border-box;
}

.table-form-content-collapsible {
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
}

.table-form-content-collapsed {
  max-height: 0px;
  opacity: 0;
}

.table-form-content-expanded {
  max-height: 1000px;
  opacity: 1;
}

.table-form-row {
  display: flex;
}

.table-form-row-border {
  border-bottom: 1px solid #d7d7d7;
}

.table-form-item {
  display: flex;
}

.table-form-item-border {
  border-right: 1px solid #e8e8e8;
}

.table-form-label {
  width: 200px;
  min-width: 120px;
  text-align: center;
  padding: 8px;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: none;
  border-right: 1px solid rgb(223, 223, 223);
  background-color: rgb(244, 244, 244);
}

.table-form-label-content {
  display: flex;
  align-items: center;
}

.table-form-label-required {
  color: red;
  margin-left: 4px;
}

.table-form-label-title {
  font-size: 16px;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

.table-form-control {
  flex: 1;
  padding: 8px;
  display: flex;
  align-items: center;
  border: none;
}

.table-form-control-item {
  margin-bottom: 0;
  width: 100%;
}

/* 去掉表格内输入框的边框 */
.table-form-control input,
.table-form-control textarea,
.table-form-control select {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent;
}

.table-form-control input:focus,
.table-form-control textarea:focus,
.table-form-control select:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 表单校验错误提示样式 */
.table-form-control-item .ant-form-item-explain-error {
  font-size: 12px;
  color: #ff4d4f;
  margin-top: 4px;
  padding-left: 0;
  line-height: 1.4;
}

.table-form-control-item .ant-form-item-has-error input,
.table-form-control-item .ant-form-item-has-error textarea,
.table-form-control-item .ant-form-item-has-error select {
  border: 1px solid #ff4d4f !important;
  background-color: #fff2f0;
}

.table-form-control-item .ant-form-item-has-error input:focus,
.table-form-control-item .ant-form-item-has-error textarea:focus,
.table-form-control-item .ant-form-item-has-error select:focus {
  border: 1px solid #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}
