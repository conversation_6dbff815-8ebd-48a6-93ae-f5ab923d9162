import React, {useEffect} from 'react';
import {Button, message, Row} from "antd";
import {Form} from "@codingapi/form-pc";
import {FormField, FormInstance} from "@codingapi/ui-framework";
import {TableForm} from "./TableForm";
import {FormInput,FormDate, FormSelect, FormSlider, FormStepper, FormTextArea} from "../../src";

const FooterButtons: React.FC<{ formInstance: FormInstance }> = ({formInstance}) => {
    const data = {
        user: {
            name: '张三',
            age: 18,
            password: '123456',
            code: '123',
            checkbox: '1,2',
            radio: '1',
            rate: 3,
            slider: 50,
            switch: true,
            textarea: '这是一段文本',
            date: '2021-08-01',
            cascader: '1,1-1,1-1-1',
            select: '1-1-1,2',
            avatar: 'c84fb304c180f61bb7db40efef7f85b7',
            color: '#000000',
            ideCode: 'console.log("hello world")'
        }
    }

    return (
        <div
            style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gridRowGap: "10px",
                gridColumnGap: "10px"
            }}
        >
            <Button
                onClick={async () => {
                    const name = formInstance.getFieldValue(["user", "name"])
                    message.success(name);
                }}
            >获取姓名</Button>

            <Button
                onClick={async () => {
                    formInstance.setFieldValue(["user", "name"], "123")
                }}
            >设置姓名</Button>

            <Button
                onClick={async () => {
                    const result = await formInstance.validate();
                    if (result) {
                        message.success("验证通过");
                    } else {
                        message.error("验证失败");
                    }
                }}
            >验证表单</Button>

            <Button
                onClick={async () => {
                    await formInstance.submit();
                }}
            >提交表单</Button>

            <Button
                onClick={async () => {
                    const values = formInstance.getFieldsValue();
                    message.success(JSON.stringify(values));
                }}
            >获取表单值</Button>

            <Button
                onClick={async () => {
                    formInstance.reset();
                }}
            >重置表单</Button>

            <Button
                onClick={async () => {
                    formInstance.setFieldsValue(data);
                }}
            >表单赋值</Button>
            <div></div>

            <Button
                onClick={async () => {
                    formInstance.enable(["user", "name"]);
                }}
            >启用姓名字段</Button>

            <Button
                onClick={async () => {
                    formInstance.disable(["user", "name"]);
                }}
            >禁用姓名字段</Button>


            <Button
                onClick={async () => {
                    formInstance.hidden(["user", "name"]);
                }}
            >隐藏姓名字段</Button>

            <Button
                onClick={async () => {
                    formInstance.show(["user", "name"]);
                }}
            >展示姓名字段</Button>

            <Button
                onClick={async () => {
                    formInstance.remove(["user", "name"]);
                }}
            >删除姓名字段</Button>

            <Button
                onClick={async () => {
                    formInstance.create({
                        props: {
                            required: true,
                            name: ['user', 'name'],
                            label: '姓名',
                            placeholder: '请输入姓名',
                            validateFunction: async (content) => {
                                const value = content.value;
                                if (value) {
                                    return []
                                }
                                return ['姓名不能为空']
                            }
                        },
                        type: 'input'
                    }, 0);
                }}
            >添加姓名字段</Button>

        </div>
    )
}

const App = () => {
    const leftFormInstance = Form.useForm();
    const rightFormInstance = Form.useForm();

    const fields = [
        {
            type: 'input',
            props: {
                required: true,
                name: ['user', 'name'],
                label: '姓名',
                placeholder: '请输入姓名',
                validateFunction: async (content) => {
                    const value = content.value;
                    if (value) {
                        return []
                    }
                    return ['姓名不能为空']
                }
            }
        },
        {
            type: 'stepper',
            props: {
                required: true,
                name: ['user', 'age'],
                label: '年龄',
                placeholder: '请输入年龄',
            }
        },
        {
            type: 'password',
            props: {
                required: true,
                name: ['user', 'password'],
                label: '银行卡密码',
                placeholder: '请输入银行卡密码',
                validateFunction: async (content) => {
                    const value = content.value;
                    if (value) {
                        return []
                    }
                    return ['银行卡密码不能为空']
                }
            }
        },
        {
            type: 'captcha',
            props: {
                required: true,
                name: ['user', 'code'],
                label: '银行卡验证码',
                placeholder: '请输入银行卡验证码',
                onCaptchaRefresh: async () => {
                    console.log('refresh captcha')
                    return {
                        url: '/captcha.jpeg',
                        code: '123'
                    }
                }
            }
        },
        {
            type: 'checkbox',
            props: {
                required: true,
                name: ['user', 'checkbox'],
                label: '复选框',
                options: [
                    {label: '选项1', value: '1'},
                    {label: '选项2', value: '2'},
                    {label: '选项3', value: '3'},
                ]
            }
        },
        {
            type: 'radio',
            props: {
                required: true,
                name: ['user', 'radio'],
                label: '单选框',
                options: [
                    {label: '选项1', value: '1'},
                    {label: '选项2', value: '2'},
                    {label: '选项3', value: '3'},
                ]
            }
        },
        {
            type: 'rate',
            props: {
                required: true,
                name: ['user', 'rate'],
                label: '评分',
            }
        },
        {
            type: 'slider',
            props: {
                required: true,
                name: ['user', 'slider'],
                label: '滑块',
                sliderPopover: true
            }
        },
        {
            type: 'switch',
            props: {
                required: true,
                name: ['user', 'switch'],
                label: '开关',
            }
        },
        {
            type: 'textarea',
            props: {
                required: true,
                name: ['user', 'textarea'],
                label: '文本域',
            }
        },
        {
            type: 'date',
            props: {
                required: true,
                name: ['user', 'date'],
                label: '日期',
            }
        },
        {
            type: 'cascader',
            props: {
                required: true,
                name: ['user', 'cascader'],
                label: '级联选择',
                options: [
                    {
                        label: '选项1',
                        value: '1',
                        children: [
                            {
                                label: '选项1-1',
                                value: '1-1',
                                children: [
                                    {
                                        label: '选项1-1-1',
                                        value: '1-1-1',
                                    },
                                    {
                                        label: '选项1-1-2',
                                        value: '1-1-2',
                                    },
                                ]
                            },
                            {
                                label: '选项1-2',
                                value: '1-2',
                            },
                        ]
                    },
                    {
                        label: '选项2',
                        value: '2',
                        children: [
                            {
                                label: '选项2-1',
                                value: '2-1',
                            },
                            {
                                label: '选项2-2',
                                value: '2-2',
                            },
                        ]
                    },
                ]
            }
        },
        {
            type: 'select',
            props: {
                required: true,
                name: ['user', 'select'],
                label: '选择器',
                selectMultiple: true,
                options: [
                    {
                        label: '选项1', value: '1',
                        children: [
                            {
                                label: '选项1-1',
                                value: '1-1',
                                children: [
                                    {label: '选项1-1-1', value: '1-1-1'},
                                    {label: '选项1-1-2', value: '1-1-2'},
                                ]
                            },
                            {label: '选项1-2', value: '1-2'},
                        ]
                    },
                    {label: '选项2', value: '2'},
                    {label: '选项3', value: '3'},
                ]
            }
        },
        {
            type: 'uploader',
            props: {
                required: true,
                name: ['user', 'avatar'],
                label: '头像',
            }
        },
        {
            type: 'color',
            props: {
                required: true,
                name: ['user', 'color'],
                label: '颜色',
            }
        },
        {
            type: 'code',
            props: {
                required: true,
                name: ['user', 'ideCode'],
                label: '代码',
            }
        },
    ] as FormField[];

    useEffect(() => {
        leftFormInstance.setFieldsValue({
            user: '张三',
            id: '123456'
        })
    }, []);

    return (
        <>
            <Button
                type="primary"
                onClick={async ()=>{
                    try {
                      const formData = leftFormInstance.getFieldsValue();
                        console.log('表单校验通过，提交数据:', formData);
                        // 使用submit方法进行校验和提交
                        await leftFormInstance.submit();
                    } catch (errorInfo) {
                        console.log('表单校验失败:', errorInfo);
                        message.error('请检查表单中的错误信息');
                    }
                }}
                style={{ marginRight: 8 }}
            >
                提交表单
            </Button>
            <Button
                onClick={(e)=>{
                    leftFormInstance.resetFields([]);
                    message.info('表单已重置');
                    const formData = leftFormInstance.getFieldsVale();
                    console.log(formData,'重置后的数据')
                }}
            >
                重置表单
            </Button>
            <Row>
                <TableForm
                    pageHeader={{
                        title: "工作信息",
                        left: "部门：技术部",
                        right: "最后更新：2023-10-01 16:45"
                    }}
                    tableHeader={{
                        title: "个人基础信息",
                        showTitle: true, // 显示标题栏
                        collapsible: true,
                        defaultCollapsed: false,
                        hidden: [] // 隐藏手机号字段
                    }}
                    formInstance={leftFormInstance}
                    initialValues={{
                        user: '',
                        age: undefined,
                        phone: '',
                        email: '',
                        address: '',
                        employeeId: '',
                        position: undefined
                    }}
                    validationRules={{
                        user: [
                            { required: true, message: '请输入姓名' },
                            { min: 2, message: '姓名至少2个字符' },
                            { max: 10, message: '姓名不能超过10个字符' }
                        ],
                        age: [
                            { required: true, message: '请输入年龄' },
                            { type: 'number', min: 18, max: 65, message: '年龄必须在18-65岁之间' }
                        ],
                        phone: [
                            { required: true, message: '请输入手机号码' },
                            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式' }
                        ],
                        email: [
                            { required: true, message: '111请输入邮箱地址' },
                            { type: 'email', message: '请输入正确的邮箱格式' }
                        ],
                        address: [
                            { required: true, message: '请输入详细地址' },
                            { min: 10, message: '地址至少10个字符' },
                            { max: 100, message: '地址不能超过100个字符' }
                        ],
                        employeeId: [
                            { required: true, message: '请输入员工编号' },
                            { pattern: /^[A-Z]{2}\d{6}$/, message: '员工编号格式：2位大写字母+6位数字，如：AB123456' }
                        ],
                        position: [
                            { required: true, message: '请选择职位' }
                        ]
                    }}
                    onFinish={async (values) => {
                        console.log('表单校验通过，提交数据:', values);
                        message.success('表单提交成功！');
                        // 这里可以添加提交到服务器的逻辑
                    }}
                >
                   <TableForm.Item
                        label="姓名"
                        name="user"
                        required
                        span={12}
                    >
                        <FormInput placeholder="请输入姓名" />
                    </TableForm.Item>
                    <TableForm.Item
                        label="年龄"
                        name="age"
                        span={12}
                        hidden={false}
                    >
                        <FormStepper placeholder="请输入年龄" />
                    </TableForm.Item>
                    <TableForm.Item
                        label="手机号"
                        name="phone"
                        span={12}
                    >
                        <FormInput placeholder="请输入手机号" />
                    </TableForm.Item>
                    <TableForm.Item
                        label="邮箱"
                        name="email"
                        span={12}
                    >
                        <FormInput placeholder="请输入邮箱地址" />
                    </TableForm.Item>
                    <TableForm.Item
                        label="地址"
                        name="address"
                        span={24}
                    >
                        <FormTextArea textAreaRows={5} placeholder="请输入详细地址" />
                    </TableForm.Item>
                </TableForm>
                <TableForm
                    tableHeader={{
                        title: "工作信息配置",
                        showTitle: false, // 隐藏标题栏
                        collapsible: true,
                        defaultCollapsed: false,
                        hidden: ['experience', 'skillLevel'] // 隐藏工作经验和技能等级字段
                    }}
                    formInstance={leftFormInstance}
                >
                    <TableForm.Item
                        label="员工编号"
                        name="employeeId"
                        required
                        span={12}
                    >
                        <FormInput placeholder="请输入员工编号" />
                    </TableForm.Item>
                    <TableForm.Item
                        label="职位"
                        name="position"
                        span={12}
                    >
                        <FormSelect
                            placeholder="请选择职位"
                            options={[
                                { label: '前端工程师', value: 'frontend' },
                                { label: '后端工程师', value: 'backend' },
                                { label: '产品经理', value: 'pm' },
                                { label: '设计师', value: 'designer' }
                            ]}
                        />
                    </TableForm.Item>
                </TableForm>
            </Row>
            {/*<Row gutter={[24, 24]}>*/}
            {/*    <Col span={12}>*/}
            {/*        <Form*/}
            {/*            form={leftFormInstance}*/}
            {/*            layout={"horizontal"}*/}
            {/*            onFinish={async (values) => {*/}
            {/*                message.success(JSON.stringify(values));*/}
            {/*            }}*/}
            {/*            footer={(*/}
            {/*                <FooterButtons*/}
            {/*                    formInstance={leftFormInstance}*/}
            {/*                />*/}
            {/*            )}*/}
            {/*        >*/}
            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "name"]}*/}
            {/*                label={"姓名"}*/}
            {/*                placeholder={"请输入姓名"}*/}
            {/*                type={'input'}*/}
            {/*                validateFunction={async (content) => {*/}
            {/*                    const value = content.value;*/}
            {/*                    if (value) {*/}
            {/*                        return []*/}
            {/*                    }*/}
            {/*                    return ['姓名不能为空']*/}
            {/*                }}*/}
            {/*            >*/}
            {/*            </FormItem>*/}


            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "age"]}*/}
            {/*                label={"年龄"}*/}
            {/*                type={"stepper"}*/}
            {/*                placeholder={"请输入年龄"}*/}
            {/*            >*/}
            {/*            </FormItem>*/}


            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "password"]}*/}
            {/*                label={"银行卡密码"}*/}
            {/*                type={"password"}*/}
            {/*                placeholder={"请输入银行卡密码"}*/}
            {/*                validateFunction={async (content) => {*/}
            {/*                    const value = content.value;*/}
            {/*                    if (value) {*/}
            {/*                        return []*/}
            {/*                    }*/}
            {/*                    return ['银行卡密码不能为空']*/}
            {/*                }}*/}
            {/*            >*/}
            {/*            </FormItem>*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "code"]}*/}
            {/*                label={"银行卡验证码"}*/}
            {/*                type={"captcha"}*/}
            {/*                placeholder={"请输入银行卡验证码"}*/}
            {/*                onCaptchaRefresh={async () => {*/}
            {/*                    console.log('refresh captcha')*/}
            {/*                    return {*/}
            {/*                        url: '/captcha.jpeg',*/}
            {/*                        code: '123'*/}
            {/*                    }*/}
            {/*                }}*/}
            {/*            >*/}
            {/*            </FormItem>*/}


            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "checkbox"]}*/}
            {/*                label={"复选框"}*/}
            {/*                type={"checkbox"}*/}
            {/*                selectMultiple={true}*/}
            {/*                options={[*/}
            {/*                    {label: '选项1', value: '1'},*/}
            {/*                    {label: '选项2', value: '2'},*/}
            {/*                    {label: '选项3', value: '3'},*/}
            {/*                ]}*/}
            {/*            >*/}
            {/*            </FormItem>*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "radio"]}*/}
            {/*                label={"单选框"}*/}
            {/*                type={"radio"}*/}
            {/*                options={[*/}
            {/*                    {label: '选项1', value: '1'},*/}
            {/*                    {label: '选项2', value: '2'},*/}
            {/*                    {label: '选项3', value: '3'},*/}
            {/*                ]}*/}
            {/*            >*/}
            {/*            </FormItem>*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "rate"]}*/}
            {/*                label={"评分"}*/}
            {/*                type={'rate'}*/}
            {/*            />*/}


            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "slider"]}*/}
            {/*                label={"滑块"}*/}
            {/*                type={'slider'}*/}
            {/*                sliderPopover={true}*/}
            {/*            >*/}
            {/*            </FormItem>*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "switch"]}*/}
            {/*                label={"开关"}*/}
            {/*                type={"switch"}*/}
            {/*            />*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "textarea"]}*/}
            {/*                label={"文本域"}*/}
            {/*                type={'textarea'}*/}
            {/*            />*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "date"]}*/}
            {/*                label={"日期"}*/}
            {/*                type={"date"}*/}
            {/*            />*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "cascader"]}*/}
            {/*                label={"级联选择"}*/}
            {/*                options={[*/}
            {/*                    {*/}
            {/*                        label: '选项1',*/}
            {/*                        value: '1',*/}
            {/*                        children: [*/}
            {/*                            {*/}
            {/*                                label: '选项1-1',*/}
            {/*                                value: '1-1',*/}
            {/*                                children: [*/}
            {/*                                    {*/}
            {/*                                        label: '选项1-1-1',*/}
            {/*                                        value: '1-1-1',*/}
            {/*                                    },*/}
            {/*                                    {*/}
            {/*                                        label: '选项1-1-2',*/}
            {/*                                        value: '1-1-2',*/}
            {/*                                    },*/}
            {/*                                ]*/}
            {/*                            },*/}
            {/*                            {*/}
            {/*                                label: '选项1-2',*/}
            {/*                                value: '1-2',*/}
            {/*                            },*/}
            {/*                        ]*/}
            {/*                    },*/}
            {/*                    {*/}
            {/*                        label: '选项2',*/}
            {/*                        value: '2',*/}
            {/*                        children: [*/}
            {/*                            {*/}
            {/*                                label: '选项2-1',*/}
            {/*                                value: '2-1',*/}
            {/*                            },*/}
            {/*                            {*/}
            {/*                                label: '选项2-2',*/}
            {/*                                value: '2-2',*/}
            {/*                            },*/}
            {/*                        ]*/}
            {/*                    },*/}
            {/*                ]}*/}
            {/*                type={'cascader'}*/}
            {/*            >*/}
            {/*            </FormItem>*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "select"]}*/}
            {/*                label={"选择器"}*/}
            {/*                selectMultiple={true}*/}
            {/*                type={'select'}*/}
            {/*                options={[*/}
            {/*                    {*/}
            {/*                        label: '选项1', value: '1',*/}
            {/*                        children: [*/}
            {/*                            {*/}
            {/*                                label: '选项1-1',*/}
            {/*                                value: '1-1',*/}
            {/*                                children: [*/}
            {/*                                    {label: '选项1-1-1', value: '1-1-1'},*/}
            {/*                                    {label: '选项1-1-2', value: '1-1-2'},*/}
            {/*                                ]*/}
            {/*                            },*/}
            {/*                            {label: '选项1-2', value: '1-2'},*/}
            {/*                        ]*/}
            {/*                    },*/}
            {/*                    {label: '选项2', value: '2'},*/}
            {/*                    {label: '选项3', value: '3'},*/}
            {/*                ]}*/}
            {/*            >*/}
            {/*            </FormItem>*/}


            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "avatar"]}*/}
            {/*                label={"头像"}*/}
            {/*                type={"uploader"}*/}
            {/*            >*/}
            {/*            </FormItem>*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "color"]}*/}
            {/*                label={"颜色"}*/}
            {/*                type={"color"}*/}
            {/*            />*/}

            {/*            <FormItem*/}
            {/*                required={true}*/}
            {/*                name={["user", "ideCode"]}*/}
            {/*                label={"代码"}*/}
            {/*                type={'code'}*/}
            {/*            />*/}
            {/*        </Form>*/}
            {/*    </Col>*/}

            {/*    <Col span={12}>*/}
            {/*        <Form*/}
            {/*            onFinish={async (values) => {*/}
            {/*                message.success(JSON.stringify(values));*/}
            {/*            }}*/}
            {/*            form={rightFormInstance}*/}
            {/*            footer={(*/}
            {/*                <FooterButtons*/}
            {/*                    formInstance={rightFormInstance}*/}
            {/*                />*/}
            {/*            )}*/}
            {/*            loadFields={async () => {*/}
            {/*                return fields;*/}
            {/*            }}*/}
            {/*        >*/}
            {/*        </Form>*/}
            {/*    </Col>*/}
            {/*</Row>*/}

        </>
    );
}

export default App;
