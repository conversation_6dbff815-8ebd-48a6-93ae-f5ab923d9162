@use "../styles/variable" as *;

.descriptions-list {
  padding: 3px;
  background-color: white;

  .descriptions-list-item {
    background-color: white;
    padding: 15px;
    font-size: $content-font-size;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid $body-background-color;

    .descriptions-list-item-label {
      font-weight: bold;
    }

    .descriptions-list-item-value {
    }
  }

  .form-header-title {
    width: 100%;
    height: 20px;
    border-left: 4px solid $theme-primary-color;
    padding-left: 4px;
    font-size: 14px;
    margin-bottom: 10px;
  }
}


