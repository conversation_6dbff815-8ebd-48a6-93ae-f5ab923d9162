import React from "react";
import {Form} from "@codingapi/form-pc";
import {NamePath} from "@codingapi/ui-framework";

interface FormValidatorProps {
    name?: NamePath;
    required?: boolean;
    rules?: any[];
    validationRules?: Record<string, any[]>;
    children: React.ReactNode;
    className?: string;
}

export const FormValidator: React.FC<FormValidatorProps> = (props) => {
    const fieldName = Array.isArray(props.name) ? props.name.join('.') : props.name;
    const validationRules = props.validationRules?.[fieldName as string] || props.rules;

    return (
        <Form.Item
            name={props.name}
            required={props.required}
            rules={validationRules}
            className={props.className}
        >
            {React.cloneElement(props.children as any, {
                style: { width: '100%' },
            })}
        </Form.Item>
    );
};