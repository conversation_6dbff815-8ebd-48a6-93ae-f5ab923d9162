{"name": "@codingapi/form-pc", "version": "0.0.55", "description": "A UI Framework built with React and TypeScript", "keywords": ["ui", "framework", "react", "typescript"], "homepage": "https://github.com/codingapi/form-pc", "bugs": {"url": "https://github.com/codingapi/form-pc/issues"}, "repository": {"type": "git", "url": "https://github.com/codingapi/form-pc.git"}, "main": "dist/index.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "license": "Apache-2.0", "files": ["dist"], "peerDependencies": {"antd": "^5.0.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"@codingapi/ui-framework": "^0.0.55", "monaco-editor": "^0.52.2"}, "scripts": {"clean": "rm -rf dist", "build": "yarn clean && rollup -c --bundleConfigAsCjs", "push": "yarn build && yarn publish --access public", "test": "jest", "test:watch": "jest --watch"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "antd": "^5.24.8", "babel-jest": "^29.7.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "rollup": "^4.9.1", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-typescript2": "^0.36.0", "sass": "^1.87.0", "ts-jest": "^29.3.2", "typescript": "^5.6.2"}}