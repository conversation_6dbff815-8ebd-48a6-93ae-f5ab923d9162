{"name": "playground", "version": "0.1.0", "private": true, "dependencies": {"@codingapi/form-pc": "file:../src", "react": "^18.3.1", "react-dom": "^18.3.1", "web-vitals": "^2.1.4"}, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1"}, "scripts": {"dev": "rsbuild start --config rsbuild.config.dev.ts", "start": "rsbuild start", "mock": "rsbuild start --config rsbuild.config.mock.ts", "build": "rsbuild build"}, "devDependencies": {"@rsbuild/core": "^1.4.7", "@rsbuild/plugin-react": "^1.3.4", "@rsbuild/plugin-sass": "^1.3.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^29.5.14", "@types/node": "^16.18.108", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.5", "css-loader": "^7.1.2", "express": "^4.21.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mockjs": "^1.1.0", "monaco-editor-webpack-plugin": "^7.1.0", "sass": "^1.89.2", "sass-loader": "^16.0.1", "style-loader": "^4.0.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}