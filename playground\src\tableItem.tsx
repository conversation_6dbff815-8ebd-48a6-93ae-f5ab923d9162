import React,{useEffect} from "react";
import {Form as AntForm} from "antd";
import {FormFactory,FormItemProps} from "@codingapi/ui-framework";
import {formFieldInit} from "@codingapi/form-pc/Form/common";

interface FormItemRenderProps extends FormItemProps{
    span: number;
    type?:string;
    rules?: any[]; 
    title?: string; 
    showTitle?: boolean; 
    children:React.ReactNode;
}

export const TableItem: React.FC<FormItemRenderProps> = (props) => {
    const formItem =  FormFactory.getInstance().create({
            type: props.type,
            span: props.span,
            rules: props.rules, 
            title: props.title, 
            showTitle: props.showTitle, 
            children:props.children,
            props: {
                ...props,
            }
        }) as React.ReactNode;
        const {formContext} = formFieldInit(props);
    
        useEffect(() => {
            formContext?.addFormField({
                type:props.type,
                props:{
                    ...props,
                }
            });
        }, []);
    return (
        <AntForm.Item
            name={props.name}
            label={props.label}
            rules={props.rules}
            required={props.required}
            help={props.help}
            tooltip={props.tooltip}
            hidden={props.hidden}
        >
            {formItem}
            {/* {React.cloneElement(props.children as any,{
              style:{width:'100%'},
            })} */}
        </AntForm.Item>
    )
}
