import React from "react";
import {FormInstance, NamePath} from "@codingapi/ui-framework";
import {Form} from "@codingapi/form-pc";
import './index.css';
interface PageHeaderProps {
    title?: string;           // 页面主标题
    left?: React.ReactNode;   
    right?: React.ReactNode;  
}

interface TableHeaderProps {
    title?: string;           // 表格标题（蓝色栏显示）
    showTitle?: boolean;      // 是否显示标题栏，默认true
    collapsible?: boolean;    // 是否可折叠，默认true
    defaultCollapsed?: boolean; // 默认是否折叠，默认false
    hidden?: string[];        // 需要隐藏的字段名数组
}

interface TableFormProps {
    children: React.ReactNode;
    formInstance?: FormInstance;
    onFinish?: (values: any) => Promise<void>;
    initialValues?: any;
    pageHeader?: PageHeaderProps;  // 页面级header（顶部显示）
    tableHeader?: TableHeaderProps; // 表格级header
    validationRules?: Record<string, any[]>; // 统一的校验规则配置
}

export const TableFormComponent: React.FC<TableFormProps> = (props) => {
    console.log('传递的props值:', { pageHeader: props.pageHeader, tableHeader: props.tableHeader });

    // 设置默认值
    const pageHeader = props.pageHeader;

    const tableHeader = props.tableHeader || {
        title: '数据表格',
        showTitle: true,
        collapsible: true,
        defaultCollapsed: false
    };

    const [isCollapsed, setIsCollapsed] = React.useState(tableHeader.defaultCollapsed || false);
    const contentRef = React.useRef<HTMLDivElement>(null);
    const items = React.Children.toArray(props.children) as React.ReactElement<TableFormComponentItemProps>[];
    console.log(items,'111111111')
    // 获取需要隐藏的字段名数组
    const hiddenFields = tableHeader.hidden || [];

    // 过滤掉隐藏的项目（支持两种方式：item.props.hidden 或 tableHeader.hidden）
    const visibleItems = items.filter(item => {
        const fieldName = Array.isArray(item.props.name) ? item.props.name.join('.') : item.props.name;
        const isHiddenByTableHeader = hiddenFields.includes(fieldName as string);
        const isHiddenByProps = item.props.hidden;
        return !isHiddenByTableHeader && !isHiddenByProps;
    });

    // 将 Item 按照 span 聚合成每行 24 的形式
    const rows: React.ReactElement[][] = [];
    let currentRow: React.ReactElement[] = [];
    let currentSpan = 0;

    visibleItems.forEach((item) => {
        const span = item.props.span || 24;
        if (currentSpan + span > 24) {
            rows.push(currentRow);
            currentRow = [item];
            currentSpan = span;
        } else {
            currentRow.push(item);
            currentSpan += span;
        }
    });
    if (currentRow.length > 0) {
        rows.push(currentRow);
    }

    return (
        <div className="table-form-container">
            <Form
                form={props.formInstance}
                onFinish={props.onFinish}
                initialValues={props.initialValues}
            >
                {pageHeader && (
                    <div className="table-form-page-header">
                      <h1 className="table-form-page-title">
                          {pageHeader.title}
                      </h1>
                      <div className="table-form-page-info">
                        <div className="table-form-page-info-left">{pageHeader.left}</div>
                        <div className="table-form-page-info-right">{pageHeader.right}</div>
                      </div>
                    </div>
                )}
                <div className="table-form-wrapper">
                  {(tableHeader.showTitle !== false) && (
                    <div
                      className={`table-form-header ${tableHeader.collapsible ? 'table-form-header-clickable' : ''}`}
                      style={{
                          cursor: tableHeader.collapsible ? 'pointer' : 'default'
                      }}
                      onClick={tableHeader.collapsible ? () => setIsCollapsed(!isCollapsed) : undefined}
                    >
                      {/* {tableHeader.collapsible && (
                          <span className={`table-form-header-arrow ${isCollapsed ? 'table-form-header-arrow-collapsed' : ''}`}>
                              ▼
                          </span>
                      )} */}
                      <span className="table-form-header-arrow"></span>
                      <span>{tableHeader.title}</span>
                    </div>
                  )}
                  <div
                    ref={contentRef}
                    className={`table-form-content ${(tableHeader.showTitle !== false && tableHeader.collapsible) ? 'table-form-content-collapsible' : ''} ${(tableHeader.showTitle !== false && tableHeader.collapsible && isCollapsed) ? 'table-form-content-collapsed' : 'table-form-content-expanded'}`}
                  >
                      {rows.map((row, rowIndex) => {
                          // 计算当前行的总span值
                          const totalSpan = row.reduce((sum, item) => sum + (item.props.span || 24), 0);

                          return (
                              <div key={rowIndex} className={`table-form-row ${rowIndex === rows.length - 1 ? '' : 'table-form-row-border'}`}>
                                  {row.map((item, index) => {
                                      const span = item.props.span || 24;
                                      // 根据span值计算该item占用的宽度百分比
                                      const itemWidthPercentage = (span / totalSpan) * 100;

                                      return (
                                          <div key={index} className={`table-form-item ${index === row.length - 1 ? '' : 'table-form-item-border'}`} style={{
                                              width: `${itemWidthPercentage}%`
                                          }}>
                                              <div className="table-form-label">
                                                  <div className="table-form-label-content">
                                                      {item.props.label}
                                                      {item.props.required && <span className="table-form-label-required">*</span>}
                                                  </div>
                                                  {item.props.showTitle && item.props.title && (
                                                      <div className="table-form-label-title">
                                                        {item.props.title}
                                                      </div>
                                                  )}
                                              </div>
                                              <div className="table-form-control">
                                                  <Form.Item
                                                      name={item.props.name}
                                                      required={item.props.required}
                                                      rules={(() => {
                                                          const fieldName = Array.isArray(item.props.name) ? item.props.name.join('.') : item.props.name;
                                                          return props.validationRules?.[fieldName as string] || item.props.rules;
                                                      })()}
                                                      className="table-form-control-item"
                                                  >
                                                      {React.cloneElement(item.props.children as any, {
                                                          style: { width: '100%' },
                                                      })}
                                                  </Form.Item>
                                              </div>
                                          </div>
                                      );
                                  })}
                              </div>
                          );
                      })}
                  </div>
                </div>
            </Form>
        </div>
    );
};

interface TableFormComponentItemProps {
    span?: number; // 1 ~ 24
    label?: React.ReactNode;
    name?: NamePath;
    children: React.ReactNode;
    required?: boolean;
    hidden?: boolean;
    title?: string; // 员工title字段
    showTitle?: boolean; // 控制title字段是否显示
    rules?: any[]; // 校验规则
}

export const TableFormComponentItem: React.FC<TableFormComponentItemProps> = (_props) => {
    // 不实际渲染，由父组件解析 props 后统一渲染
    return null;
};

type TableFormType = typeof TableFormComponent;
type TableComponentType = TableFormType & {
    Item: typeof TableFormComponentItem;
};

export const TableForm = TableFormComponent as TableComponentType;
TableForm.Item = TableFormComponentItem;
